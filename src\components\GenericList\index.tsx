import React from 'react';
import { Flex, Typography, Checkbox, Pagination } from 'antd';
import type { PaginationProps } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';

const { Text } = Typography;

/**
 * 列配置接口
 */
export interface IColumnConfig<T = any> {
  /** 列标识 */
  key: string;
  /** 列标题 */
  title?: string;
  /** 列宽度 */
  width?: string;
  /** 渲染函数 */
  render: (record: T, index: number) => React.ReactNode;
  /** 是否可选择 */
  selectable?: boolean;
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right';
}

/**
 * 通用列表组件属性接口
 */
export interface IGenericListProps<T = any> {
  /** 数据源 */
  dataSource: T[];
  /** 列配置 */
  columns: IColumnConfig<T>[];
  /** 行选择配置 */
  rowSelection?: TableRowSelection<T>;
  /** 是否显示选择模式 */
  showSelection?: boolean;
  /** 分页配置 */
  pagination?: PaginationProps;
  /** 行点击事件 */
  onRowClick?: (record: T, index: number) => void;
  /** 获取行键的函数 */
  rowKey?: string | ((record: T) => string);
  /** 自定义行样式类名 */
  rowClassName?: string;
  /** 自定义容器样式类名 */
  className?: string;
  /** 空数据时的显示内容 */
  emptyText?: React.ReactNode;
  /** 行间距 */
  gap?: number;
}

/**
 * 通用列表组件
 * @param props 组件属性
 * @returns JSX元素
 */
const GenericList = <T extends Record<string, any>>(
  props: IGenericListProps<T>
): React.ReactElement => {
  const {
    dataSource = [],
    columns = [],
    rowSelection,
    showSelection = false,
    pagination,
    onRowClick,
    rowKey = 'id',
    rowClassName = 'border rounded-xl border-gray-200 py-2 px-4 hover:bg-gray-50 mb-2 h-[40px]',
    className = 'bg-white',
    emptyText = '暂无数据',
    gap = 16,
  } = props;

  /**
   * 获取行键值
   * @param record 记录数据
   * @returns 行键
   */
  const getRowKey = (record: T): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey] as string;
  };

  /**
   * 处理行点击事件
   * @param record 记录数据
   * @param index 索引
   */
  const handleRowClick = (record: T, index: number) => {
    onRowClick?.(record, index);
  };

  /**
   * 处理选择框变化
   * @param record 记录数据
   * @param checked 是否选中
   */
  const handleSelectionChange = (record: T, checked: boolean) => {
    if (!rowSelection?.onChange) return;

    const recordKey = getRowKey(record);
    const selectedKeys = rowSelection.selectedRowKeys || [];
    
    const newSelectedKeys = checked
      ? [...selectedKeys, recordKey]
      : selectedKeys.filter(key => key !== recordKey);
    
    rowSelection.onChange(newSelectedKeys);
  };

  /**
   * 渲染单行数据
   * @param record 记录数据
   * @param index 索引
   * @returns JSX元素
   */
  const renderRow = (record: T, index: number) => {
    const recordKey = getRowKey(record);
    const isSelected = rowSelection?.selectedRowKeys?.includes(recordKey);

    return (
      <div
        key={recordKey}
        className={rowClassName}
        onClick={() => handleRowClick(record, index)}
      >
        <Flex gap={gap}>
          {/* 序号列 */}
          <div className="w-16">
            <span className="text-[#999] text-[12px]">{index + 1}</span>
          </div>

          {/* 动态渲染列 */}
          {columns.map((column) => {
            const { key, width, render, align = 'left' } = column;
            
            return (
              <div
                key={key}
                className={width || 'flex-1'}
                style={{ textAlign: align }}
              >
                {render(record, index)}
              </div>
            );
          })}

          {/* 选择列 */}
          {showSelection && rowSelection && (
            <div className="w-32 flex justify-end">
              <Checkbox
                checked={isSelected}
                onChange={(e) => handleSelectionChange(record, e.target.checked)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          )}
        </Flex>
      </div>
    );
  };

  return (
    <div className={className}>
      {/* 列表内容 */}
      {dataSource.length > 0 ? (
        dataSource.map((record, index) => renderRow(record, index))
      ) : (
        <div className="text-center py-8 text-gray-500">
          {emptyText}
        </div>
      )}

      {/* 分页 */}
      {pagination && pagination.total > 0 && (
        <div className="flex justify-end py-4">
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            showSizeChanger={pagination.showSizeChanger}
            showQuickJumper={pagination.showQuickJumper}
            showTotal={pagination.showTotal}
            onChange={pagination.onChange}
            onShowSizeChange={pagination.onShowSizeChange}
          />
        </div>
      )}
    </div>
  );
};

export default GenericList;