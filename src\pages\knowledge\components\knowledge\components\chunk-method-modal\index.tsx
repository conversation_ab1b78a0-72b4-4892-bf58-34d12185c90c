import { IModalManagerChildrenProps } from '../modal-manager';
import { Form, InputNumber, Modal, Select } from 'antd';
import React, { useEffect } from 'react';

import { DocumentParserType } from '../../constants/knowledge';
import { IParserConfig } from '../../interfaces/database/document';
import { IChangeParserConfigRequestBody } from '../../interfaces/request/document';
import styles from './index.module.less';

interface IProps extends Omit<IModalManagerChildrenProps, 'showModal'> {
  loading: boolean;
  onOk: (
    parserId: DocumentParserType | undefined,
    parserConfig: IChangeParserConfigRequestBody
  ) => void;
  showModal?(): void;
  parserId: DocumentParserType;
  parserConfig: IParserConfig;
  documentExtension: string;
  documentId: string;
}

// 分块决策选项
const chunkDecisionOptions = [{ label: '智能分块', value: 'smart_chunk' }];

const ChunkMethodModal: React.FC<IProps> = ({
  documentId,
  parserId,
  onOk,
  hideModal,
  visible,
  documentExtension,
  parserConfig,
  loading,
}) => {
  const [form] = Form.useForm();

  /**
   * 处理表单提交
   */
  const handleOk = async () => {
    const values = await form.validateFields();
    const parser_config = {
      chunk_decision: values.chunk_decision,
      chunk_size: values.chunk_size,
      min_chunk_size: values.min_chunk_size,
    };
    onOk(parserId, parser_config);
  };

  /**
   * 表单重置
   */
  const afterClose = () => {
    form.resetFields();
  };

  /**
   * 初始化表单数据
   */
  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        chunk_decision: parserConfig?.chunk_decision || 'smart_chunk',
        chunk_size: parserConfig?.chunk_size || 512,
        min_chunk_size: parserConfig?.min_chunk_size || 50,
      });
    }
  }, [form, parserConfig, visible]);

  return (
    <Modal
      title={'分块规则'}
      open={visible}
      onOk={handleOk}
      onCancel={hideModal}
      afterClose={afterClose}
      confirmLoading={loading}
      width={640}
      centered
      okText="解析"
    >
      <Form form={form} layout="vertical" autoComplete="off">
        {/* 分块决策 */}
        <Form.Item
          name="chunk_decision"
          label="分块决策"
          rules={[
            {
              required: true,
              message: '请选择分块决策',
            },
          ]}
        >
          <Select placeholder="请选择分块决策" options={chunkDecisionOptions} />
        </Form.Item>

        {/* 分块大小 */}
        <Form.Item
          name="chunk_size"
          label={
            <>
              <span>分块大小</span>
              <span className="text-[#939393] text-[12px] ml-[6px] leading-2">
                单位：tokens，范围：50-2048
              </span>
            </>
          }
          rules={[
            {
              required: true,
              message: '请输入分块大小',
            },
            {
              type: 'number',
              min: 50,
              max: 2048,
              message: '分块大小必须在50-2048之间',
            },
          ]}
        >
          <InputNumber
            placeholder="请输入分块大小"
            min={50}
            max={2048}
            style={{ width: '100%' }}
          />
        </Form.Item>

        {/* 最小分块大小 */}
        <Form.Item
          name="min_chunk_size"
          label={
            <>
              <span>最小分块大小</span>
              <span className="text-[#939393] text-[12px] ml-[6px] leading-2">
                单位：tokens，范围：10-500
              </span>
            </>
          }
          rules={[
            {
              required: true,
              message: '请输入最小分块大小',
            },
            {
              type: 'number',
              min: 10,
              max: 500,
              message: '最小分块大小必须在10-500之间',
            },
          ]}
        >
          <InputNumber
            placeholder="请输入最小分块大小"
            min={10}
            max={500}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default ChunkMethodModal;
