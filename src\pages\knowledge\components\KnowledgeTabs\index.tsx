import { useEffect, useState } from 'react';
import { Tabs } from '@arco-design/web-react';
import { useNavigate, useLocation, Routes, Route } from 'react-router-dom';
import FileManager from '../file-manager/src/components/file-manager';
import KnowledgeList from '../knowledge/components/KnowledgeList';
import AiStaffList from '../aiStaff';
import styles from './style/index.module.less';
import AiStaffInfo from '../aiStaff/info';

const { TabPane } = Tabs;

/**
 * 知识库标签页组件 - 使用子路由方式实现tab切换
 * @returns JSX元素
 */
function KnowledgeTabs() {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('files');

  /**
   * 根据当前路径设置活跃的tab
   */
  useEffect(() => {
    const pathname = location.pathname;
    if (
      pathname.endsWith('/knowledge/files') ||
      pathname.includes('/knowledge/files/')
    ) {
      setActiveTab('files');
    } else if (
      pathname.endsWith('/knowledge/knowledge') ||
      pathname.includes('/knowledge/knowledge/')
    ) {
      setActiveTab('knowledge');
    } else if (
      pathname.endsWith('/knowledge/aiStaff') ||
      pathname.includes('/knowledge/aiStaff/')
    ) {
      setActiveTab('aiStaff');
    } else if (pathname === '/knowledge' || pathname === '/knowledge/') {
      // 默认跳转到files页面
      navigate('/knowledge/files', { replace: true });
    }
  }, [location.pathname, navigate]);

  /**
   * 处理Tab切换 - 通过路由跳转实现
   * @param key tab的key值
   */
  const handleTabChange = (key: string) => {
    // 根据tab key跳转到对应的子路由
    switch (key) {
      case 'files':
        navigate('/knowledge/files');
        break;
      case 'knowledge':
        navigate('/knowledge/knowledge');
        break;
      case 'aiStaff':
        navigate('/knowledge/aiStaff');
        break;
      default:
        navigate('/knowledge/files');
    }
  };

  return (
    <div className={styles.knowledgeTabContainer}>
      <Tabs
        activeTab={activeTab}
        onChange={handleTabChange}
        className={styles.tabs}
      >
        <TabPane key="files" title="文件">
          {/* 内容通过子路由渲染 */}
        </TabPane>
        <TabPane key="knowledge" title="知识库">
          {/* 内容通过子路由渲染 */}
        </TabPane>
        <TabPane key="aiStaff" title="员工">
          {/* 内容通过子路由渲染 */}
        </TabPane>
      </Tabs>

      {/* 子路由内容区域 */}
      <div className={styles.tabContent}>
        <Routes>
          <Route path="files" element={<FileManager />} />
          <Route path="knowledge" element={<KnowledgeList />} />
          <Route path="aiStaff" element={<AiStaffList />} />
          {/* <Route path="aiStaff/create" element={<AiStaffCreate />} /> */}
          <Route path="aiStaff/info" element={<AiStaffInfo />} />
          {/* <Route path="aiStaff/edit/:id" element={<AiStaffEdit />} /> */}
          {/* 默认路由重定向到files */}
          <Route index element={<FileManager />} />
        </Routes>
      </div>
    </div>
  );
}

export default KnowledgeTabs;
