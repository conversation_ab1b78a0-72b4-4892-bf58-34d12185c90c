import { useState, useEffect, useRef } from 'react';
import { Button, Grid, Message, Input, Select } from '@arco-design/web-react';
import styles from './style/index.module.less';
import useLocale from '@/utils/useLocale';
import { Form } from '@arco-design/web-react';
import appIcon from '@/assets/application/appIcon1.png';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import Text from '@arco-design/web-react/es/Typography/text';
import { IconClose } from '@arco-design/web-react/icon';
import { AgentResponse } from '@/lib/services/agent-service';
import UploadIconW from '@/assets/application/upload-w.svg';
import IconCloseTag from '@/assets/close.svg';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const TextArea = Input.TextArea;

interface ApplicationBasicProps {
  agentData?: AgentResponse | null;
  loading: boolean;
  onAgentDataUpdate?: (newData: Partial<AgentResponse>) => void;
  isEditing: boolean;
  onAgentBasicUpdate?: (data: any) => void;
  basicData: any;
}

function AiStaffBasic({
  basicData,
  agentData,
  loading: parentLoading,
  onAgentDataUpdate,
  isEditing,
  onAgentBasicUpdate,
}: ApplicationBasicProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const locale = useLocale();
  const [form] = Form.useForm();
  const [uploadLoading, setUploadLoading] = useState(false);
  const uploadRef = useRef<any>(null);

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [agentName, setAgentName] = useState('');

  const [labels, setLabels] = useState<string[]>([]);
  const [permission, setPermission] = useState<'public' | 'private'>('public');

  const [nameExists, setNameExists] = useState(false);

  const isFallbackAgent = agentData?.name?.endsWith('-Fallback Agent') || false;
  // 假设你有如下表单state
  const [name, setName] = useState(basicData?.name || '');
  const [isPublic, setIsPublic] = useState(basicData?.is_public || true);
  const [description, setDescription] = useState('');

  const fetchData = async () => {
    if (basicData) {
      // 处理labels数组
      const labels = Array.isArray(basicData.labels)
        ? basicData.labels
            .map((item) => {
              try {
                return typeof item === 'string'
                  ? item.replace(/[\[\]"\\]/g, '').trim()
                  : item;
              } catch {
                return item;
              }
            })
            .filter((item) => typeof item === 'string' && item.length > 0)
        : [];

      setAgentName(basicData.name);
      setDescription(basicData.description);
      setLabels(labels);
      setPermission(basicData.permission || 'public');
      // 设置表单初始值
      form.setFieldsValue({
        name: basicData.name,
        description: basicData.description,
        labels: labels,
        permission: basicData.permission || 'public',
      });
    } else {
      // 新建模式：不设置任何默认值，完全依赖用户输入
      form.resetFields();
      setAgentName('');
      setDescription('');
      setLabels([]);
      setPreviewUrl(null);
      setPermission('');
    }
  };

  useEffect(() => {
    if (!isInitialized) {
      fetchData();
      setIsInitialized(true);
    }
  }, []);

  useEffect(() => {
    if (!isEditing && isInitialized) {
      fetchData();
    }
  }, [isEditing]);

  // // 监听表单数据变化
  // useEffect(() => {
  //   const validLabels = labels.filter((label) => label.trim() !== '');
  //   const updateData = {
  //     name: agentName,
  //     description,
  //   };
  //   onAgentDataUpdate(updateData);
  // }, [agentName, description, labels, previewUrl, permission]);

  // 每次表单数据变更时同步到父组件
  useEffect(() => {
    if (onAgentBasicUpdate) {
      onAgentBasicUpdate({
        name,
        description,
        labels,
        isPublic,
      });
    }
  }, [name, description, labels, isPublic]);

  const handleUpload = async (file: File) => {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      Message.error('只能上传图片文件');
      return false;
    }

    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      Message.error('图片大小不能超过2MB');
      return false;
    }

    setUploadLoading(true);
    try {
      // TODO: 模拟上传
      const formData = new FormData();
      formData.append('file', file);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      const uploadUrl = 'YOUR_UPLOAD_API_URL';
      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('上传失败');
      }

      const result = await response.json();
      setPreviewUrl(result.url);
    } catch (error) {
      console.error('上传失败:', error);
      Message.error('上传失败');
    } finally {
      setUploadLoading(false);
    }
  };

  const handleRemove = async () => {
    if (!agentData?.id) return;

    try {
      setPreviewUrl(null);
      Message.success('图标已移除');
    } catch (error) {
      console.error('移除图标失败:', error);
      Message.error('移除图标失败');
    }
  };

  const handleAddLabel = () => {
    if (labels.length >= 3) return;
    const newLabels = [...labels];
    newLabels.push('');
    setLabels(newLabels);
    form.setFieldValue('labels', newLabels);
  };

  return (
    <div className={styles.container}>
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
        style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
      >
        <div style={{ flex: 1 }}>
          <RowComponent style={{ marginTop: 16 }}>
            <div className={styles.iconContainer}>
              {/* 图标 */}
              {/* <Upload
                ref={uploadRef}
                accept="image/*"
                showUploadList={false}
                beforeUpload={handleUpload}
                disabled={uploadLoading || !isEditing}
              > */}
              <div className={styles.previewBox}>
                <div className={styles.uploadHoverIcon}>
                  <UploadIconW />
                </div>
                {previewUrl || agentData?.icon_url ? (
                  <div className={styles.iconWrapper}>
                    <img
                      src={previewUrl || agentData?.icon_url}
                      alt="应用图标"
                    />
                    <Button
                      type="text"
                      status="danger"
                      icon={<IconClose />}
                      className={styles.removeIcon}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemove();
                      }}
                    />
                  </div>
                ) : (
                  <div className={styles.placeholderBox}>
                    {uploadLoading ? (
                      <div className={styles.loadingWrapper}>
                        <Text>上传中...</Text>
                      </div>
                    ) : (
                      <img
                        src={appIcon}
                        alt="应用图标"
                        className={styles.plusIcon}
                      />
                    )}
                  </div>
                )}
              </div>
              {/* </Upload> */}

              <div className={styles.divider}></div>

              {/* 命名 */}
              <div className={styles.nameContainer}>
                <RowComponent>
                  <Text className={styles.subtitle}>
                    {locale['menu.application.info.basic.names']}
                  </Text>
                </RowComponent>
                <RowComponent style={{ marginTop: 8, width: '100%' }}>
                  <FormItem
                    field="name"
                    rules={[
                      { required: true, message: '请输入AI员工名称' },
                      {
                        validator: (value, callback) => {
                          if (nameExists) {
                            callback('该名称已存在，请使用其他名称');
                          } else {
                            callback();
                          }
                        },
                      },
                    ]}
                    validateTrigger={['onBlur', 'onChange']}
                  >
                    <TextArea
                      allowClear
                      disabled={!isEditing || isFallbackAgent}
                      placeholder={
                        locale[
                          'menu.application.agent.info.basic.placeholder.names'
                        ]
                      }
                      value={name}
                      onChange={(value) => {
                        setName(value);
                        form.setFieldValue('name', value);
                      }}
                      style={{
                        backgroundColor: '#fff',
                        border: nameExists
                          ? '1px solid #f53f3f'
                          : '1px solid #e5e6eb',
                        resize: 'none',
                        height: '40px',
                        borderRadius: '8px',
                        opacity: isFallbackAgent ? 0.6 : 1,
                      }}
                    />
                  </FormItem>
                </RowComponent>
              </div>
            </div>
          </RowComponent>

          {/* 描述 */}
          <RowComponent style={{ marginTop: 16 }}>
            <Text className={styles.subtitle}>
              {locale['menu.application.info.basic.descript']}
            </Text>
          </RowComponent>
          <RowComponent style={{ marginTop: 8 }}>
            <FormItem
              field="description"
              rules={[{ required: true }]}
              validateTrigger={['onBlur', 'onChange']}
              style={{ marginBottom: 0 }}
            >
              <div style={{ position: 'relative', width: '50%' }}>
                <TextArea
                  disabled={!isEditing}
                  placeholder={
                    locale['menu.application.info.basic.placeholder.descript']
                  }
                  maxLength={200}
                  value={description}
                  onChange={(value) => {
                    setDescription(value);
                    form.setFieldValue('description', value);
                  }}
                  style={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e6eb',
                    width: '100%',
                    resize: 'none',
                    height: '120px',
                    borderRadius: '8px',
                  }}
                />
                <div
                  style={{
                    position: 'absolute',
                    bottom: '8px',
                    right: '8px',
                    fontSize: '12px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    pointerEvents: 'none',
                  }}
                >
                  {description?.length}/200
                </div>
              </div>
            </FormItem>
          </RowComponent>
          {/* 权限控制下拉框 */}
          <RowComponent style={{ marginTop: 16 }}>
            <FormItem
              field="permission"
              label={'权限'}
              style={{ width: '50%' }}
            >
              <Select
                disabled={!isEditing}
                value={isPublic ? 'public' : 'private'}
                onChange={(value) => {
                  setIsPublic(value === 'public');
                  form.setFieldValue('permission', value); // 修正：应该设置 permission 字段
                }}
                style={{ width: '100%' }}
              >
                <Select.Option value="private">个人</Select.Option>
                <Select.Option value="public">所有人可访问</Select.Option>
              </Select>
            </FormItem>
          </RowComponent>
          {/* 标签 */}
          <RowComponent className={styles.titleRow}>
            <div className={styles.titleContent}>
              <Text className={styles.subtitle}>
                {locale['menu.application.info.basic.label']}
              </Text>
              <Text className={styles.subtitlePlaceholder}>
                {locale['menu.application.info.basic.placeholder.label']}
              </Text>
            </div>
            <Button
              className={styles.addLabelBut}
              disabled={labels.length >= 3 || !isEditing}
              onClick={handleAddLabel}
              style={{
                opacity: labels.length >= 3 || !isEditing ? 0.5 : 1,
                cursor:
                  labels.length >= 3 || !isEditing ? 'not-allowed' : 'pointer',
              }}
            >
              <Text className={styles.operateText}>
                {locale['menu.application.template.setting.adds']}
              </Text>
            </Button>
          </RowComponent>
          <Col span={24} className={styles.labelContainer}>
            {/* 渲染已选择的标签 */}
            {labels.length > 0 && (
              <div className={styles.selectedItemList}>
                {labels.map((label, index) => (
                  <Row
                    key={`${label}-${index}`}
                    className={styles.selectedItemRow}
                  >
                    <Input
                      value={label}
                      autoFocus={true}
                      disabled={!isEditing}
                      onChange={(value) => {
                        if (value && value.length > 10) {
                          return;
                        }
                        const newLabels = [...labels];
                        newLabels[index] = value;
                        setLabels(newLabels);
                        form.setFieldValue('labels', newLabels);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          e.stopPropagation();
                        }
                      }}
                      placeholder={locale[
                        'menu.application.create.group.form.label.placeholder'
                      ].replace('{index}', (index + 1).toString())}
                      suffix={
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer',
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            const newLabels = [...labels];
                            newLabels.splice(index, 1);
                            setLabels(newLabels);
                            form.setFieldValue('labels', newLabels);
                          }}
                        />
                      }
                      className={styles.selectedItemCol}
                    />
                  </Row>
                ))}
              </div>
            )}
          </Col>
        </div>
      </Form>
    </div>
  );
}

export default AiStaffBasic;
