import DocumentToolbar from './document-toolbar';
import {
  useGetRowSelection,
  useCreateEmptyDocument,
  useNavigateToOtherPage,
  useRenameDocument,
  useChangeDocumentParser,
  useShowMetaModal,
  useHandleRunDocumentByIds,
} from './hooks';
import type { IDocumentInfo } from '../interfaces/document';
import type { IChangeParserConfigRequestBody } from '../interfaces/request/document';
import { DocumentParserType } from '../constants/knowledge';
import { useFetchNextDocumentList } from '../hooks/document-hooks';
import CreateFileModal from './create-file-modal';
import { useSetSelectedRecord } from '../hooks/logic-hooks';
import RenameModal from './rename-modal';
import { getExtension } from '../utils/document-util';
import ChunkMethodModal from '../components/chunk-method-modal';
import FilePicker from '../components/file-picker';
import { SetMetaModal } from './set-meta-modal';
import { useNavigate } from 'react-router-dom';
import { IconLeft } from '@arco-design/web-react/icon';
import { useHandleAddFileToKnowledge } from './hooks';
import DocumentList from '../components/document-list';

const KnowledgeFile = () => {
  const navigate = useNavigate();
  const { rowSelection, showSelection, setShowSelection} = useGetRowSelection();
  const { searchString, documents, pagination, handleInputChange } = useFetchNextDocumentList();

  const {
    createLoading,
    onCreateOk,
    createVisible,
    hideCreateModal,
    showCreateModal,
  } = useCreateEmptyDocument();

  const { toChunk } = useNavigateToOtherPage();

  const {
    addFileToKnowledgeVisible,
    hideAddFileToKnowledgeModal,
    showAddFileToKnowledgeModal,
    onAddFileToKnowledgeOk,
  } = useHandleAddFileToKnowledge();

  const { currentRecord, setRecord } = useSetSelectedRecord<IDocumentInfo>();

  const {
    renameLoading,
    onRenameOk,
    renameVisible,
    hideRenameModal,
    showRenameModal,
  } = useRenameDocument(currentRecord.id);

  const {
    changeParserLoading,
    onChangeParserOk,
    changeParserVisible,
    hideChangeParserModal,
    showChangeParserModal,
  } = useChangeDocumentParser(currentRecord.id);

  const { handleRunDocumentByIds, loading: runLoading } = useHandleRunDocumentByIds(currentRecord.id);

  /**
   * 处理切片方法弹窗确认：保存配置后直接执行解析
   * @param parserId 解析器ID
   * @param parserConfig 解析器配置
   */
  const handleChunkMethodModalOk = async (parserId: DocumentParserType | undefined, parserConfig: IChangeParserConfigRequestBody) => {
    // 先保存配置
    await onChangeParserOk(parserId, parserConfig);
    // 配置保存成功后执行解析
    await handleRunDocumentByIds(currentRecord.id, false, false);
  };

  const {
    showSetMetaModal,
    hideSetMetaModal,
    setMetaVisible,
    setMetaLoading,
    onSetMetaModalOk,
  } = useShowMetaModal(currentRecord.id);

  return (
    <>
      <div
        className="flex items-center gap-3 cursor-pointer hover:bg-gray-50 rounded-lg p-3 transition-colors duration-200 w-fit font-bold"
        onClick={(e) => {
          e.stopPropagation();
          navigate('/knowledge/knowledge');
        }}
      >
        <div className="flex items-center justify-center w-8 h-8 duration-200">
          <IconLeft />
        </div>
        <span className="text-[18px] font-medium text-[#333] transition-colors duration-200">
          文件列表
        </span>
      </div>
      <DocumentToolbar
        selectedRowKeys={rowSelection.selectedRowKeys as string[]}
        showCreateModal={showCreateModal}
        // showDocumentUploadModal={showDocumentUploadModal}
        showDocumentUploadModal={showAddFileToKnowledgeModal}
        searchString={searchString}
        handleInputChange={handleInputChange}
        documents={documents}
        total={pagination.total}
        setShowSelection={setShowSelection}
      ></DocumentToolbar>

      {/* 文档列表 */}
      <DocumentList
        documents={documents}
        rowSelection={rowSelection}
        showSelection={showSelection}
        pagination={pagination}
        onDocumentClick={toChunk}
        setCurrentRecord={setRecord}
        showRenameModal={showRenameModal}
        showChangeParserModal={showChangeParserModal}
        showSetMetaModal={showSetMetaModal}
      />
      <CreateFileModal
        visible={createVisible}
        hideModal={hideCreateModal}
        loading={createLoading}
        onOk={onCreateOk}
      />

      <FilePicker
        multiple
        title="选择文件"
        visible={addFileToKnowledgeVisible}
        onCancel={hideAddFileToKnowledgeModal}
        onConfirm={onAddFileToKnowledgeOk}
      />

      <RenameModal
        visible={renameVisible}
        onOk={onRenameOk}
        loading={renameLoading}
        hideModal={hideRenameModal}
        initialName={currentRecord.name}
      ></RenameModal>

      <ChunkMethodModal
        documentId={currentRecord.id}
        parserId={currentRecord.parser_id as DocumentParserType}
        parserConfig={currentRecord.parser_config}
        documentExtension={getExtension(currentRecord.name)}
        onOk={handleChunkMethodModalOk}
        visible={changeParserVisible}
        hideModal={hideChangeParserModal}
        loading={changeParserLoading || runLoading}
      />
      {setMetaVisible && (
        <SetMetaModal
          visible={setMetaVisible}
          hideModal={hideSetMetaModal}
          onOk={onSetMetaModalOk}
          loading={setMetaLoading}
          initialMetaData={currentRecord.meta_fields}
        ></SetMetaModal>
      )}
    </>
  );
};

export default KnowledgeFile;
