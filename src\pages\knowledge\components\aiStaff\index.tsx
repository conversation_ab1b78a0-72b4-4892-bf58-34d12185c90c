import {
  Button,
  Divider,
  Input,
  Select,
  Grid,
  Typography,
  Layout,
  Tag,
  Modal,
  Space,
  Spin,
  Skeleton,
  Notification,
} from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';
import IconClose from '@/assets/acp/IconClose.svg';
import {
  useState,
  useRef,
  useImperativeHandle,
  useMemo,
  forwardRef,
  useCallback,
  useEffect,
} from 'react';
import useLocale from '@/utils/useLocale';
import IconScreen from '@/assets/application/screen.svg';
import KnowledgeCard from '../knowledge/components/KnowledgeCard';
import { Action } from '../knowledge/components/KnowledgeCard';
import { useNavigate } from 'react-router-dom';
import { formatDate } from '@/utils/date';
import IconEmptyModelConfig from '@/assets/model/IconEmptyModelConfig.svg';
import InfiniteScroll from 'react-infinite-scroll-component';
import KnowledgeModal from '../knowledge/components/KnowledgeModal';
// 新增：引入listEmployees
import {
  listEmployees,
  enableEmployee,
  disableEmployee,
  deleteEmployee,
  getEmployeeTag,
} from '../knowledge/services/aiStaff-service';
import AiStaffDeleteConfirmModal from './AiStaffDeleteConfirmModal';
import { useDispatch } from 'react-redux';

const Option = Select.Option;
const { Row, Col } = Grid;
const { Text } = Typography;
const { Header, Content } = Layout;

// AI员工列表
export default function AiStaffList() {
  const locale = useLocale();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // 搜索和分页参数
  const [searchString, setSearchString] = useState('');
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [hasMore, setHasMore] = useState(true);
  const [list, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  // 标签相关状态
  const [tags, setTags] = useState<string[]>([]);
  const [selectedTag, setSelectedTag] = useState<string>('');

  // 新增：获取当前用户id
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const userId = user.id;

  // 面包屑处理函数
  const updateBreadcrumbData = (newBreadcrumb: Map<string, string>) => {
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
  };

  // 跳转到创建页并设置面包屑
  const gotoCreate = () => {
    const breadcrumbData = new Map<string, string>([
      ['/knowledge/aiStaff/info', '新建AI员工'],
    ]);
    updateBreadcrumbData(breadcrumbData);
    navigate('/knowledge/aiStaff/info', { state: { id: null } });
  };

  // 获取标签列表
  const fetchTags = async () => {
    try {
      const response = await getEmployeeTag();
      setTags(response.data || []);
    } catch (error) {
      console.error('获取标签列表失败:', error);
    }
  };

  // 组件加载时获取标签列表
  useEffect(() => {
    fetchTags();
  }, []);

  // 获取员工列表
  const fetchEmployees = async (nextPage = 1, append = false) => {
    setLoading(true);
    const res = await listEmployees({
      Pager: {
        Page: nextPage,
        Size: pageSize,
      },
      CreateUserId: userId,
      ...(searchString ? { Name: searchString } : {}),
      ...(selectedTag ? { Tags: selectedTag } : {}),
    });
    const employees = res?.items || [];
    const totalCount = res?.count || 0;
    setTotal(totalCount);
    setHasMore(employees.length === pageSize);
    setList(append ? [...list, ...employees] : employees);
    setLoading(false);
  };

  // 首次加载和搜索
  useMemo(() => {
    fetchEmployees(1, false);
    setPage(1);
    // eslint-disable-next-line
  }, [searchString, selectedTag]);

  // 无限滚动加载下一页
  const fetchNextPage = () => {
    const nextPage = page + 1;
    fetchEmployees(nextPage, true);
    setPage(nextPage);
  };

  const knowledgeModalRef = useRef<{
    open: (form?: any) => void;
  }>();

  const aiStaffDeleteConfirmModalRef = useRef<{
    open: (form?: any) => void;
  }>();

  const getActionButtons = useCallback((row: any) => {
    // 编辑、删除按钮
    const actions: Action[] = [
      { label: '编辑', className: 'text-[#333333]', type: 'edit' },
      { label: '删除', className: 'text-[#333333]', type: 'del' },
    ];
    // 动态添加启用/禁用按钮
    if (row.disabled) {
      actions.push({
        label: '启用',
        className: 'text-[#52c41a]',
        type: 'enable',
      });
    } else {
      actions.push({
        label: '禁用',
        className: 'text-[#d54941]',
        type: 'disable',
      });
    }
    return actions;
  }, []);

  const handleAction = async (row: any, type: Action['type']) => {
    switch (type) {
      case 'start':
        break;
      case 'edit':
        navigate(`/knowledge/aiStaff/info`, {
          state: { id: row.id, path: 'info' },
        });
        break;
      case 'del':
        aiStaffDeleteConfirmModalRef.current?.open(row);
        break;
      case 'enable':
        try {
          await enableEmployee(row.id);
          Notification.success({ title: '成功', content: '启用成功' });
          fetchEmployees(1, false);
          setPage(1);
        } catch (e) {
          Notification.error({ title: '失败', content: '启用失败' });
          console.error(e);
        }
        break;
      case 'disable':
        try {
          await disableEmployee(row.id);
          Notification.success({ title: '成功', content: '禁用成功' });
          fetchEmployees(1, false);
          setPage(1);
        } catch (e) {
          Notification.error({ title: '失败', content: '禁用失败' });
          console.error(e);
        }
        break;
    }
  };

  // 删除逻辑可后续适配
  const confirmDelete = async (data) => {
    try {
      await deleteEmployee(data.id);
      Notification.success({ title: '成功', content: '删除成功' });
      fetchEmployees(1, false);
      setPage(1);
    } catch (e) {
      Notification.error({ title: '失败', content: '删除失败' });
      console.error(e);
    }
  };

  const handleSwitch = async (row: any, value: boolean) => {
    // value为true表示启用，false表示禁用
    try {
      if (value) {
        await enableEmployee(row.id);
      } else {
        await disableEmployee(row.id);
      }
      // 操作成功后刷新列表
      fetchEmployees(1, false);
      setPage(1);
    } catch (e) {
      // 可选：错误提示
      // message.error('操作失败');
      console.error(e);
    }
  };

  const handleToDataset = (row: any) => {
    navigate(`/knowledge/dataset?id=${row.id}`);
  };

  return (
    <>
      <Header className="flex justify-between items-center">
        <Button
          className="h-[40px] p-[8px_24px] rounded-[8px]"
          size="large"
          type="primary"
          onClick={gotoCreate}
        >
          创建员工
        </Button>
        <Row align="center" gutter={8}>
          <Col flex="auto">
            <Text className="text-[#adadad]">共 {total} 个AI员工</Text>
          </Col>
          <Col flex="240px">
            <Input
              prefix={<IconSearch />}
              placeholder="AI搜索..."
              allowClear
              onChange={(v, e) => setSearchString(e.target.value)}
            />
          </Col>
          <Col flex="160px">
            <Select
              className="w-full"
              placeholder="标签"
              prefix={<IconScreen />}
              allowClear={true}
              value={selectedTag}
              onChange={(value) => setSelectedTag(value)}
              triggerProps={{
                autoAlignPopupWidth: false,
                autoAlignPopupMinWidth: true,
                position: 'bl',
                className: 'knowledge-select-popup',
              }}
            >
              <Option value="">全部</Option>
              {tags.map((tag) => (
                <Option key={tag} value={tag}>
                  {tag}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Header>
      <Divider />
      <Content className="basis-0 overflow-auto" id="scrollableDiv">
        <Spin
          className="block h-full relative [&_.arco-spin-children]:h-full"
          loading={loading}
        >
          <InfiniteScroll
            dataLength={list?.length ?? 0}
            next={fetchNextPage}
            hasMore={hasMore}
            loader={
              <Skeleton
                className="mt-5"
                image={{ shape: 'circle' }}
                text={{
                  rows: 2,
                }}
              />
            }
            endMessage={!!total && <Divider>没有更多数据了 🤐</Divider>}
            scrollableTarget="scrollableDiv"
            scrollThreshold="200px"
            style={{
              overflow: 'unset',
            }}
          >
            <div className="grid grid-cols-4 gap-4">
              {list?.length > 0 ? (
                list.map((item, index) => {
                  const tags = item?.tags?.length ? item.tags : [];
                  return (
                    <KnowledgeCard
                      key={`${item?.name}`}
                      tag={
                        <>
                          {tags.map((tag, tagIndex) => (
                            <Tag
                              key={tagIndex}
                              className="rounded-[4px] bg-white text-[#5c5c5c] border-[#ebebeb]"
                              bordered
                            >
                              {tag}
                            </Tag>
                          ))}
                        </>
                      }
                      footerTag={
                        <Tag
                          className="rounded-[4px] transition-opacity duration-300"
                          color={item.disabled ? 'red' : 'green'}
                        >
                          {item.disabled ? '禁用' : '启用'}
                        </Tag>
                      }
                      switchChecked={!item.disabled}
                      actionButtons={getActionButtons(item)}
                      description={item.description || item.Description}
                      title={item.name || item.Name}
                      createTime={formatDate(
                        item.createdTime,
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                      handleSwitch={(value) => handleSwitch(item, value)}
                      handleAction={(type) => handleAction(item, type)}
                    />
                  );
                })
              ) : (
                <div className="absolute w-full h-full flex items-center justify-center bg-white">
                  <Space
                    direction="vertical"
                    size={16}
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <IconEmptyModelConfig style={{ width: 80, height: 80 }} />
                    <Text type="secondary">未找到匹配的AI员工</Text>
                  </Space>
                </div>
              )}
            </div>
          </InfiniteScroll>
        </Spin>
      </Content>
      <KnowledgeModal ref={knowledgeModalRef} />
      <AiStaffDeleteConfirmModal
        confirmDelete={confirmDelete}
        ref={aiStaffDeleteConfirmModalRef}
      />
    </>
  );
}
