import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Tabs, Message, Spin } from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import localStyles from './style/index.module.less';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import ApplicationBasic from './components/basic';
import ApplicationSettings from './components/settings';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  getAgentDetail,
  updateAgent,
  createAgent,
  AgentResponse,
  bindAcpTools,
} from '@/lib/services/agent-service';
import { getAcpToolsById } from '@/lib/services/acp-server-service';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import ButtonComponent from '@arco-design/web-react/es/Button';
import Text from '@arco-design/web-react/es/Typography/text';
import {
  createEmployee,
  getEmployeeDetail,
} from '@/pages/knowledge/components/knowledge/services/aiStaff-service';
import { updateEmployee } from '@/pages/knowledge/components/knowledge/services/aiStaff-service';

function AiStaffInfo() {
  const locale = useLocale();
  const navigate = useNavigate();
  const location = useLocation<{ id: string; path: string; atId: string }>();
  const { id, path, atId } = location.state || {};
  const [loading, setLoading] = useState(false);
  const [agentData, setAgentData] = useState<AgentResponse | null>(null);
  const [newAgentData, setNewAgentData] = useState<AgentResponse | null>(null);
  const [newEmployeeData, setNewEmployeeData] =
    useState<EmployeeResponse | null>(null);
  const [lastFetchedId, setLastFetchedId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('agentInfo');
  const [isEditing, setIsEditing] = useState(true);
  const [isFormValid, setIsFormValid] = useState(false);
  const [creating, setCreating] = useState(false);
  const [employeeId, setEmployeeId] = useState<string | null>(null);
  // 新增：基础信息表单数据
  const [basicFormData, setBasicFormData] = useState({
    name: '',
    description: '',
    labels: [],
    isPublic: false,
  });

  // 新增：basic表单数据回调
  const handleAgentBasicUpdate = useCallback((data) => {
    setBasicFormData((prev) => ({ ...prev, ...data }));
  }, []);

  const currentAgentIdRef = useRef(id);
  const currentPathRef = useRef(path);
  const currentAtIdRef = useRef(atId);

  // 验证表单是否有效
  const validateForm = useCallback((data: AgentResponse | null) => {
    if (!data) {
      return false;
    }

    // 智能体类型校验
    if (!data.type) {
      return false;
    }

    // 模型选择校验
    const llmConfig = data.llm_config;
    if (llmConfig && !llmConfig.is_inherit) {
      const hasProvider =
        llmConfig.provider && llmConfig.provider.trim() !== '';
      const hasModel = llmConfig.model && llmConfig.model.trim() !== '';

      if (hasProvider !== hasModel) {
        return false;
      }
    }

    return true;
  }, []);

  const fetchEmployeeDetail = useCallback(async () => {
    if (loading && currentAgentIdRef.current === lastFetchedId) {
      return;
    }

    try {
      setLoading(true);
      if (!currentAgentIdRef.current) {
        setIsEditing(true);
        return;
      }
      const response = await getEmployeeDetail(currentAgentIdRef.current);
      setEmployeeId(response.data.id);

      // 只使用接口返回的字段，不添加默认值
      setBasicFormData({
        name: response.data.name,
        description: response.data.description,
        labels: response.data.tags,
        isPublic: response.data.isPublic,
      });

      // 直接使用接口返回的 agent 数据，不做任何处理
      setAgentData(response.data.agent);
      setNewAgentData(response.data.agent);
      setNewEmployeeData(response.data.agent);
      setLastFetchedId(currentAgentIdRef.current);

      setIsFormValid(validateForm(response.data.agent));
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
    } finally {
      setLoading(false);
    }
  }, [currentAgentIdRef.current, loading, lastFetchedId]);

  const handleAgentDataUpdate = useCallback(
    (newData: Partial<AgentResponse>) => {
      setNewAgentData((prev) => {
        // 如果是新建模式且没有初始数据，只保存传入的字段，不使用默认模板
        if (!prev && !id) {
          const updatedData = { ...newData } as AgentResponse;
          const isValid = validateForm(updatedData);
          setIsFormValid(isValid);
          return updatedData;
        }

        // 更新模式：基于现有数据进行更新
        const updatedData = { ...prev, ...newData };

        // 验证更新后的数据
        const isValid = validateForm(updatedData);
        setIsFormValid(isValid);

        return updatedData;
      });
    },
    [validateForm, id]
  );

  useEffect(() => {
    const initData = async () => {
      setIsEditing(false);
      if (currentAgentIdRef.current) {
        // 编辑模式：获取详情数据
        await fetchEmployeeDetail();
        setIsEditing(true);
      } else {
        // 新建模式：不设置任何默认数据，完全依赖用户输入
        setLoading(false);
        setIsEditing(true);
        setBasicFormData({
          name: undefined,
          description: undefined,
          labels: undefined,
          isPublic: undefined,
        });
        setAgentData(null);
        setNewAgentData(null);
        setNewEmployeeData(null);
        setIsFormValid(false);
      }
    };

    initData();
  }, []);

  const handleAgentIdChange = (newId: string) => {
    currentAgentIdRef.current = newId;
  };

  const handleCancel = () => {
     navigate('/knowledge/aiStaff');
  };

  const handleEdit = () => {
    handleSave();
  };

  // 创建AI员工和Agent的handleSave逻辑
  const handleSave = useCallback(async () => {
    try {
      if (newAgentData?.id && isEditing) {
        // 编辑模式：使用能力名字和用户修改的字段
        const agentParams = {
          ...newAgentData, // 使用接口返回的完整数据
          // 更新时使用选择的能力名字
          ...(newAgentData?.name !== undefined && { name: newAgentData.name }),
        };
        await updateAgent(agentData?.agent_id, agentParams as any);

        // 更新Employee，只传递实际有值的字段
        const employeeParams: any = {};
        if (basicFormData.name !== undefined) employeeParams.name = basicFormData.name;
        if (basicFormData.description !== undefined) employeeParams.description = basicFormData.description;
        if (basicFormData.labels !== undefined) employeeParams.tags = basicFormData.labels;
        if (basicFormData.isPublic !== undefined) employeeParams.isPublic = basicFormData.isPublic;

        await updateEmployee(newEmployeeData.id, employeeParams);
        Message.success('保存成功');
        navigate('/knowledge/aiStaff');
      } else {
        // 新建模式：只传递必要字段
        if (!basicFormData.name) {
          Message.error('请填写AI员工名字');
          return;
        }

        // 创建Agent，只传递必要字段：提示词、新创建的名字、知识库、其他详情字段
        const agentParams: any = {
          // 从 newAgentData 中获取其他详情字段（包含选择的能力的所有信息）
          ...(newAgentData || {}),
          // 创建时使用新创建的名字（基础表单中的AI员工名字）
          name: basicFormData.name, // 新创建的名字
          // Settings 表单字段（提示词、知识库）会覆盖详情字段
          ...(newAgentData?.instruction !== undefined && { instruction: newAgentData.instruction }), // 提示词
          ...(newAgentData?.knowledge_bases !== undefined && { knowledge_bases: newAgentData.knowledge_bases }), // 知识库
        };

        const agentRes = await createAgent(agentParams as any);
        const agentId = agentRes?.agent_id || agentRes?.id;
        if (!agentId) throw new Error('智能体创建失败');

        // 创建Employee，只传递有值的字段
        const employeeParams: any = { agentId };
        // AI员工的名字来自基础表单
        if (basicFormData.name !== undefined) employeeParams.name = basicFormData.name;
        if (basicFormData.description !== undefined) employeeParams.description = basicFormData.description;
        if (basicFormData.labels !== undefined) employeeParams.tags = basicFormData.labels;
        if (basicFormData.isPublic !== undefined) employeeParams.isPublic = basicFormData.isPublic;

        // 验证AI员工名字是否填写
        if (!employeeParams.name) {
          Message.error('请填写AI员工名字');
          return;
        }

        await createEmployee(employeeParams);
        Message.success(locale['opreate.create.success'] || '创建成功');
        navigate('/knowledge/aiStaff');
      }
    } catch (e) {
      Message.error(locale['opreate.create.errMsg'] || '保存失败');
    } finally {
      setCreating(false);
    }
  }, [newAgentData, basicFormData, navigate, locale]);

  if (loading) {
    return (
      <div
        className={localStyles.customContainer}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
        }}
      >
        <Spin dot size={40} />
      </div>
    );
  }

  return (
    <div className={localStyles.customContainer}>
      <Tabs
        className={localStyles.tabs}
        activeTab={activeTab}
        onChange={setActiveTab}
      >
        {/* 应用页面内容 */}
        <TabPane
          key="agentInfo"
          title={locale['menu.application.info.header.basic']}
        >
          <ApplicationBasic
            agentData={newAgentData}
            loading={loading}
            isEditing={isEditing}
            onAgentDataUpdate={handleAgentDataUpdate}
            onAgentBasicUpdate={handleAgentBasicUpdate}
            basicData={basicFormData}
          />
        </TabPane>
        <TabPane
          key="agentSettings"
          title={locale['menu.application.info.header.settings']}
        >
          <ApplicationSettings
            agentData={newAgentData}
            loading={loading}
            isEditing={isEditing}
            onAgentDataUpdate={handleAgentDataUpdate}
            newEmployeeData={newEmployeeData}
          />
        </TabPane>
      </Tabs>

      {(activeTab === 'agentInfo' || activeTab === 'agentSettings') && (
        <div className={localStyles.footer}>
          <RowComponent className={localStyles.operateButGroup}>
            <ButtonComponent
              type="secondary"
              className={[localStyles.cancelBut, localStyles.but]}
              onClick={handleCancel}
            >
              <Text className={localStyles.text}>
                {locale['menu.application.template.setting.operate.cancel']}
              </Text>
            </ButtonComponent>
            {newAgentData?.id ? (
              <>
                {isEditing && (
                  <>
                    {isEditing ? (
                      <ButtonComponent
                        loading={loading}
                        onClick={handleEdit}
                        type="primary"
                        className={[localStyles.createBut, localStyles.but]}
                      >
                        <Text
                          className={localStyles.text}
                          style={{ color: '#FFFFFF' }}
                        >
                          {locale['editBut']}
                        </Text>
                      </ButtonComponent>
                    ) : (
                      <ButtonComponent
                        loading={loading}
                        onClick={handleSave}
                        type="primary"
                        disabled={!isFormValid || creating}
                        className={[
                          localStyles.createBut,
                          localStyles.but,
                          (!isFormValid || creating) && localStyles.disabled,
                        ]}
                      >
                        <Text
                          className={localStyles.text}
                          style={{ color: '#FFFFFF' }}
                        >
                          {locale['menu.application.opreate.save']}
                        </Text>
                      </ButtonComponent>
                    )}
                  </>
                )}
              </>
            ) : (
              <ButtonComponent
                onClick={handleSave}
                type="primary"
                className={[
                  localStyles.createBut,
                  localStyles.but,
                ]}
              >
                <Text className={localStyles.text} style={{ color: '#FFFFFF' }}>
                  {locale['menu.application.opreate.create']}
                </Text>
              </ButtonComponent>
            )}
          </RowComponent>
        </div>
      )}
    </div>
  );
}

export default AiStaffInfo;
